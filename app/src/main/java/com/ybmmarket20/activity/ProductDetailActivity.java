package com.ybmmarket20.activity;

import static com.ybmmarket20.constant.IntentCanst.ACTIVITY_ENTRANCE;
import static com.ybmmarket20.constant.IntentCanst.JG_ENTRANCE;
import static com.ybmmarket20.constant.IntentCanst.JG_REFERRER;
import static com.ybmmarket20.constant.IntentCanst.JG_REFERRER_MODULE;
import static com.ybmmarket20.constant.IntentCanst.JG_REFERRER_TITLE;
import static com.ybmmarket20.constant.IntentCanst.JG_TRACK_BEAN;
import static com.ybmmarket20.constant.IntentCanst.MODULE;
import static com.ybmmarket20.constant.IntentCanst.OFFSET;
import static com.ybmmarket20.constant.IntentCanst.PAGE_ID;
import static com.ybmmarket20.constant.IntentCanst.SHOW_ADDITIONALPURCHASE_FLAG;
import static com.ybmmarket20.constant.IntentCanst.SHOW_GROUPPURCHASE_FLAG;
import static com.ybmmarket20.constant.IntentCanst.SOURCETYPE;

import android.animation.Animator;
import android.animation.AnimatorListenerAdapter;
import android.animation.ValueAnimator;
import android.content.Context;
import android.content.Intent;
import android.graphics.Color;
import android.graphics.drawable.Drawable;
import android.os.Bundle;
import android.os.CountDownTimer;
import android.text.Html;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.ViewStub;
import android.widget.CheckBox;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.PopupWindow;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentPagerAdapter;
import androidx.lifecycle.ViewModelProvider;

import com.analysys.ANSAutoPageTracker;
import com.apkfuns.logutils.LogUtils;
import com.flyco.tablayout.SlidingTabLayout;
import com.flyco.tablayout.listener.OnTabSelectListener;
import com.github.mzule.activityrouter.annotation.Router;
import com.ybm.app.bean.NetError;
import com.ybmmarket20.R;
import com.ybmmarket20.bean.BaseBean;
import com.ybmmarket20.bean.EmptyBean;
import com.ybmmarket20.bean.ImPackUrlBean;
import com.ybmmarket20.bean.ProductDetailBean;
import com.ybmmarket20.bean.ProductDetailBeanWrapper;
import com.ybmmarket20.bean.SpellGroupShareContentBean;
import com.ybmmarket20.bean.product_detail.ReportPDExtendOuterBean;
import com.ybmmarket20.business.correction.ui.activity.MainCorrectionActivity;
import com.ybmmarket20.common.AlertDialogEx;
import com.ybmmarket20.common.BaseResponse;
import com.ybmmarket20.common.JGTrackManager;
import com.ybmmarket20.common.JGTrackTopLevelKt;
import com.ybmmarket20.common.JgTrackBean;
import com.ybmmarket20.common.LicenseStatusActivity;
import com.ybmmarket20.common.RequestParams;
import com.ybmmarket20.common.util.ConvertUtils;
import com.ybmmarket20.common.util.ToastUtils;
import com.ybmmarket20.constant.AppNetConfig;
import com.ybmmarket20.constant.IntentCanst;
import com.ybmmarket20.fragments.CommodityFragment;
import com.ybmmarket20.fragments.SpecificationFragment;
import com.ybmmarket20.home.MainActivity;
import com.ybmmarket20.message.Message;
import com.ybmmarket20.network.HttpManager;
import com.ybmmarket20.utils.DialogUtil;
import com.ybmmarket20.utils.RoutersUtils;
import com.ybmmarket20.utils.SpUtil;
import com.ybmmarket20.utils.TimeCountUtil;
import com.ybmmarket20.utils.UiUtils;
import com.ybmmarket20.utils.analysis.XyyIoUtil;
import com.ybmmarket20.view.CommonDialogLayout;
import com.ybmmarket20.view.GoodsDetailShareDialog;
import com.ybmmarket20.view.ViewPagerSlide;
import com.ybmmarketkotlin.viewmodel.CommodityViewModel;

import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import butterknife.Bind;
import butterknife.ButterKnife;
import butterknife.OnClick;


/**
 * 商品详情页面
 * productdetail_no 二维码过来的，productdetail 商品id过来的
 */
@Router({"productdetail", "productdetail_no", "productdetail_no/:product_no", "productdetail/:product_id"})
public class ProductDetailActivity extends LicenseStatusActivity implements CommodityFragment.ProductDetailScrollListener, ShareCallback, ANSAutoPageTracker {

    @Bind(R.id.title_left_b)
    ImageView titleLeftB;
    @Bind(R.id.ps_tab)
    SlidingTabLayout psTab;
    @Bind(R.id.tv_menu_b)
    ImageView tvMenuB;
    @Bind(R.id.ll_title)
    LinearLayout llTitle;
    @Bind(R.id.vp_client)
    ViewPagerSlide vpClient;
    @Bind(R.id.ll_detail)
    FrameLayout llDetail;
    //    @Bind(R.id.tv_tab)
//    TextView tvTab;
//    @Bind(R.id.detail_ll_collect)
//    LinearLayout detailLlCollect;
//    @Bind(R.id.collect_cb)
//    CheckBox collectCb;
    @Bind(R.id.vs_no_result)
    ViewStub vsNoResult;

    @Bind(R.id.ll_content)
    LinearLayout llContent;
    @Bind(R.id.cl_top_tips)
    ConstraintLayout clTopTips;
    @Bind(R.id.tv_top_tips_title)
    TextView tvTopTipsTitle;
    @Bind(R.id.tv_top_tips_title1)
    TextView tvTopTipsTitle1;
    @Bind(R.id.tv_tip_countdown_h)
    TextView tvTipCountdownH;
    @Bind(R.id.tv_tip_countdown_m)
    TextView tvTipCountdownM;
    @Bind(R.id.tv_tip_countdown_s)
    TextView tvTipCountdownS;
    @Bind(R.id.rl_detail_more)
    RelativeLayout rlDetailMore;
//    @Bind(R.id.iv_normal_share)
//    ImageView ivNormalShare;
    @Bind(R.id.ll_suixinpin)
    LinearLayout llSuixinpin;

    private List<Fragment> list_fragment;
    private ArrayList<String> list_title;
    CommodityFragment commodityFragment = new CommodityFragment();
    SpecificationFragment mSpecificationFragment = new SpecificationFragment();
    private PopupWindow mPw;
    private String productNo;
    private String productId;
    private String mIsMainProductVirtualSupplier;
    private ProductDetailBeanWrapper mDetailBean = null;
    private View prView;
    protected String producgtId;

    private float lastP = -1;
    private int searchHeight;
    private int color;
    private boolean isShowTitle = false;
    private CountDownTimer countDownTimer;
    private boolean isFavorite = false;

    private String pageId = "";
    private String module = "";
    private String offset = "";
    private String sourceType = "";
    private Boolean showGroupPurchaseFlag;  // 是否显示组合购
    private Boolean showAdditionalPurchaseFlag; // 是否显示加价购

    //极光埋点 start
    private String jgReferrer = ""; //页面来源
    private String jgReferrerTitle = ""; //页面来源标题
    private String jgReferrerModule = ""; //来源模块
    private String jgEntrance = ""; //来源路径
    private String jgActivityEntrance = ""; //来源路径
    private ReportPDExtendOuterBean jgPdExtendOuterBean;
    //极光埋点 end

    public int getAlphaColor(float f) {
        return Color.argb((int) (f * 255), 242, 242, 242);
    }

    private TimeCountUtil countUtil;
    private GoodsDetailShareDialog shareDialog;

    //是否是批购包邮品
    private boolean mIsWholeSale;


    @Override
    protected void onPause() {
        super.onPause();
        if (shareDialog != null && shareDialog.isShowing()) {
            shareDialog.dismiss();
        }
    }

    @Override
    protected void initData() {

        pageId = this.getIntent().getStringExtra(PAGE_ID);
        module = this.getIntent().getStringExtra(MODULE);
        offset = this.getIntent().getStringExtra(OFFSET);
        sourceType = this.getIntent().getStringExtra(SOURCETYPE);
        showGroupPurchaseFlag = this.getIntent().getBooleanExtra(SHOW_GROUPPURCHASE_FLAG,false);
        showAdditionalPurchaseFlag = this.getIntent().getBooleanExtra(SHOW_ADDITIONALPURCHASE_FLAG,false);
        jgReferrer = this.getIntent().getStringExtra(JG_REFERRER);
        jgReferrerTitle = this.getIntent().getStringExtra(JG_REFERRER_TITLE);
        jgReferrerModule = this.getIntent().getStringExtra(JG_REFERRER_MODULE);
        jgEntrance = this.getIntent().getStringExtra(JG_ENTRANCE);
        jgActivityEntrance = this.getIntent().getStringExtra(ACTIVITY_ENTRANCE);
        productId = this.getIntent().getStringExtra(IntentCanst.PRODUCTID);
        jgPdExtendOuterBean = (ReportPDExtendOuterBean) this.getIntent().getSerializableExtra(IntentCanst.JG_JSON_REPORT_PD_EXTEND_OUTER_BEAN);
        initFragment();
        initFragmentTitle();
        if (SpUtil.isKa()) {
//            detailLlCollect.setVisibility(View.GONE);
            psTab.setMinimumWidth(ConvertUtils.dp2px(110));
        }
        llTitle.postDelayed(new Runnable() {
            @Override
            public void run() {
                if (llTitle != null) {
                    searchHeight = llTitle.getHeight();
                }
            }
        }, 100);

        JgTrackBean jgTrackBean = new JgTrackBean();
        jgTrackBean.setJgReferrer(jgReferrer);
        jgTrackBean.setJgReferrerTitle(jgReferrerTitle);
        jgTrackBean.setJgReferrerModule(jgReferrerModule);
        jgTrackBean.setActivityEntrance(jgActivityEntrance);
        jgTrackBean.setPageId(JGTrackManager.TrackProductDetail.PAGE_ID);
        jgTrackBean.setTitle(JGTrackManager.TrackProductDetail.TITLE);
        jgTrackBean.setModule("商品详情");
        if (jgEntrance != null && !jgEntrance.isEmpty()) {
            if (!jgEntrance.contains("商品详情页")) {
                jgTrackBean.setEntrance(jgEntrance + "-商品详情页");
            } else {
                jgTrackBean.setEntrance(jgEntrance);
            }
        } else {
            jgTrackBean.setEntrance("商品详情页");
        }

        Bundle bundle = new Bundle();
        bundle.putString(PAGE_ID, pageId);
        bundle.putString(MODULE, module);
        bundle.putString(OFFSET, offset);
        bundle.putString(SOURCETYPE, sourceType);
        bundle.putSerializable(JG_TRACK_BEAN, jgTrackBean);
        bundle.putSerializable(IntentCanst.JG_JSON_REPORT_PD_EXTEND_OUTER_BEAN, jgPdExtendOuterBean);
        bundle.putBoolean(SHOW_GROUPPURCHASE_FLAG,showGroupPurchaseFlag);
        bundle.putBoolean(SHOW_ADDITIONALPURCHASE_FLAG,showAdditionalPurchaseFlag);
        bundle.putString("skuId", productId);
        commodityFragment.setArguments(bundle);
        commodityFragment.setOnScrollListener(new CommodityFragment.OnScrollListener() {
            @Override
            public void OnScroll(float scrollY) {

                if (searchHeight > 0) {

                    float p = Math.abs(scrollY) * 1.0f / searchHeight * 1.0f;

                    setAlpha(p, 0);
                }
            }
        });
        commodityFragment.setOnTopOrBottomListener(new CommodityFragment.OnTopOrBottomListener() {
            @Override
            public void onTopOrBottom(boolean isTop) {
                if (isTop) {
                    psTab.setVisibility(View.VISIBLE);
//                    tvTab.setVisibility(View.INVISIBLE);
                    vpClient.setSlide(true);
                } else {
                    psTab.setVisibility(View.INVISIBLE);
//                    tvTab.setVisibility(View.VISIBLE);
                    vpClient.setSlide(false);
                }
            }
        });
        commodityFragment.setOnCheckListener(new CommodityFragment.OnCheckListener() {
            @Override
            public void onCheck(ProductDetailBean productDetail) {
//                collectCb.setChecked(productDetail.isFavoriteStatus());
//                initCollect(productDetail, collectCb);
                isFavorite = productDetail.isFavoriteStatus();
            }

            @Override
            public void onIsFavoriteStatus(boolean isFavoriteStatus) {
//                collectCb.setChecked(isFavoriteStatus);
                isFavorite = isFavoriteStatus;
            }
        });

        commodityFragment.setRefreshListener(new CommodityFragment.RefreshGoodsDetailListener() {
            @Override
            public void refreshGoodsDetail() {
                refreshProductDetailInfo();
            }
        });

        psTab.setOnTabSelectListener(new OnTabSelectListener() {
            @Override
            public void onTabSelect(int position) {
                LogUtils.tag("onTabSelect").d(position);
                switch (position) {
                    case 0:
                        //商品
                        commodityFragment.smoothScrollToIndex(0);
                        JGTrackTopLevelKt.jgTrackProductDetailBtnClick(ProductDetailActivity.this, "商品", jgReferrer);
                        break;
                    case 1:
                        //说明书
                        commodityFragment.smoothScrollToIndex(1);
                        JGTrackTopLevelKt.jgTrackProductDetailBtnClick(ProductDetailActivity.this, "说明书", jgReferrer);
                        break;
                    case 2:
                        //推荐
//                        commodityFragment.smoothScrollToIndex(2);
                        break;
                }
            }

            @Override
            public void onTabReselect(int position) {

            }
        });


        MyDetailsAdapter adapter = new MyDetailsAdapter(getSupportFragmentManager(), list_fragment, list_title);
        vpClient.setAdapter(adapter);
        vpClient.setOffscreenPageLimit(list_title.size() + 1);
//        psTab.setViewPager(vpClient);
        psTab.setTitles(list_title);
        psTab.setIndicatorWidthEqualTitleHalf(true);
        productNo = this.getIntent().getStringExtra(IntentCanst.PRODUCTNO);
        if (!TextUtils.isEmpty(productNo)) {
            doGetNewsContent(null, productNo);
            return;
        }
        mIsMainProductVirtualSupplier = this.getIntent().getStringExtra("isMainProductVirtualSupplier");
        if (!TextUtils.isEmpty(productId)) {
            doGetNewsContent(productId, null);
            getGroupProductRecommend(productId);
            return;
        }

        ToastUtils.showShort("参数错误");
        finish();
    }

    private void setAlpha(float p, int position) {
//        if (llTitle == null) {
//            return;
//        }
//
//        if (p >= 1) {
//            p = 1;
//        }
//        if (p < 0) {
//            p = 0;
//        }
//        if (p < 0.2 && p > 0) {
//            p = 0;
//        }
//
//        if (position > 0) {
//
//        } else {
//            lastP = p;
//        }
//
//        color = getAlphaColor(p > 0.8f ? 1.0f : p);
//        llTitle.setBackgroundDrawable(new ColorDrawable(color));
//
//        psTab.setAlpha((p > 0.8f ? 1.0f : p) * 255);
//
//        if (p > 0.8f) {
//
//            if (!isShowTitle) {
//                titleLeftA.setVisibility(View.INVISIBLE);
//                tvMenuA.setVisibility(View.INVISIBLE);
//
//                titleLeftB.setVisibility(View.VISIBLE);
//                tvMenuB.setVisibility(View.VISIBLE);
//
//                collectCb.setButtonDrawable(R.drawable.selector_common_shoucang);
//
//                isShowTitle = true;
//            }
//        } else {
//
//            if (isShowTitle) {
//                titleLeftA.setVisibility(View.VISIBLE);
//                tvMenuA.setVisibility(View.VISIBLE);
//
//                titleLeftB.setVisibility(View.INVISIBLE);
//                tvMenuB.setVisibility(View.INVISIBLE);
//
//                collectCb.setButtonDrawable(R.drawable.selector_common_shoucang2);
//
//                isShowTitle = false;
//            }
//        }
    }

    private void initFragmentTitle() {
        list_title = new ArrayList<>();
        list_title.add("商品");
        list_title.add("说明书");
//        if (!isKaUser) {//ka用户不显示推荐
//            list_title.add("推荐");
//        }
    }

    /**
     * 收藏
     *
     * @param productDetail 商品详情-实体类
     * @param checkBox      是否被选中
     */
    private void initCollect(final ProductDetailBean productDetail, final CheckBox checkBox) {

//        detailLlCollect.setOnClickListener(new View.OnClickListener() {
//
//            @Override
//            public void onClick(View v) {
//
//                // 有货提醒 1: 有货提醒收藏；2：降价收藏；3.收藏已过期
//                setCollection(productDetail, checkBox, false);
//            }
//        });
    }

    private void setCollection(final ProductDetailBean productDetail, final CheckBox checkBox, final TextView textView, final PopupWindow mpw, final boolean isMore) {

        if (checkBox.isChecked()) {
            if (productDetail.businessType == 1 || productDetail.businessType == 2) {
                showSaveDialog(new AlertDialogEx.OnClickListener() {
                    @Override
                    public void onClick(AlertDialogEx dialog, int button) {
                        checkCollect(productDetail, checkBox, textView, mpw, isMore);
                    }
                }, productDetail.businessType);
            } else {
                checkCollect(productDetail, checkBox, textView, mpw, isMore);
            }
        } else {
            checkCollect(productDetail, checkBox, textView, mpw, isMore);
        }
    }

    /**
     * 收藏-取消收藏
     *
     * @param productDetail1 商品详情-实体类
     * @param collectCb      是否被选中
     *                       productId 商品id
     *                       取消收藏url => "favorite/cancelAttention"
     *                       收藏url => "favorite/attention"
     */
    private void checkCollect(final ProductDetailBean productDetail1, final CheckBox collectCb, final TextView textView, final PopupWindow mpw, final boolean isMore) {
        // 是否是更多收藏
        if (isMore) {
            JGTrackTopLevelKt.jgTrackProductDetailBtnClick(ProductDetailActivity.this, collectCb.isChecked() ? "更多-取消收藏" : "更多-收藏", jgReferrer);
        } else {
            JGTrackTopLevelKt.jgTrackProductDetailBtnClick(ProductDetailActivity.this, collectCb.isChecked() ? "取消收藏" : "收藏", jgReferrer);
        }
        final int id = productDetail1.id;
        final String collect_net = collectCb.isChecked() ? AppNetConfig.CANCEL_COLLECT : AppNetConfig.COLLECT;
        final String collect_str = collectCb.isChecked() ? "取消收藏" : "收藏成功";

        RequestParams params = new RequestParams();
        String merchantId = SpUtil.getMerchantid();
        params.put("merchantId", merchantId);
        params.put("skuId", String.valueOf(id));

        HttpManager.getInstance().post(collect_net, params, new BaseResponse<EmptyBean>() {

            @Override
            public void onSuccess(String content, BaseBean<EmptyBean> obj, EmptyBean baseBean) {

                if (null != obj) {
                    if (obj.isSuccess()) {

                        if (collectCb.isChecked()) {
                            commodityFragment.setFavoriteStatus(true);
                            collectCb.setChecked(false);
                            textView.setText("收藏");
                            DialogUtil.showCommonStatus(CommonDialogLayout.CommonTip.success, collect_str);
                        } else {
                            commodityFragment.setFavoriteStatus(false);
                            collectCb.setChecked(true);
                            textView.setText("已收藏");
                            DialogUtil.showCommonStatus(CommonDialogLayout.CommonTip.success, collect_str);
                        }
                        isFavorite = collectCb.isChecked();
                    }
                }
            }
        });

        mpw.dismiss();
    }

    private void showSaveDialog(AlertDialogEx.OnClickListener listener, int businessType) {

        String str = (businessType == 1
                ? "取消收藏后，该商品有货将不会继续通知您，确认取消收藏吗?"
                : (businessType == 2 ? "取消收藏后，该商品降价将不会继续通知您，确认取消收藏吗?" : ""));
        AlertDialogEx dialogEx = new AlertDialogEx(this);
        dialogEx.setMessage(str).setCancelButton("取消", new AlertDialogEx.OnClickListener() {
            @Override
            public void onClick(AlertDialogEx dialog, int button) {
                dialog.dismiss();
            }
        }).setCancelable(false).setConfirmButton("确定", listener).setCanceledOnTouchOutside(false).setTitle(null).show();
    }

    /**
     * 设置分享按钮和收藏按钮切换在标题栏的切换
     *
     * @param detailBean
     */
    private void setShareAndCollection(ProductDetailBeanWrapper detailBean) {
        if (detailBean == null) return;
//        ivNormalShare.setVisibility(detailBean.getIsAssemble() ? View.GONE : View.VISIBLE);
//        detailLlCollect.setVisibility(detailBean.getIsAssemble() ? View.VISIBLE : View.GONE);
    }

    /**
     * 初始化设置数据
     *
     * @param id   商品id
     * @param code 二维码url
     *             详细信息 url => "product/findProductDetail"
     */
    private void doGetNewsContent(String id, String code) {
        String merchantid = SpUtil.getMerchantid();
        RequestParams params = new RequestParams();
        params.put("merchantId", merchantid);
        if (!TextUtils.isEmpty(id)) {
            params.put("id", id);
        }
        if (!TextUtils.isEmpty(code)) {
            params.put("code", code);
        }

        if (!TextUtils.isEmpty(mIsMainProductVirtualSupplier)) {
            params.put("isMainProductVirtualSupplier", "1".equals(mIsMainProductVirtualSupplier) ? "true" : "false");
        }
        HttpManager.getInstance().post(isKaUser ? AppNetConfig.KA_DETAIL_MESSAGE : AppNetConfig.DETAIL_MESSAGE, params, new BaseResponse<ProductDetailBeanWrapper>() {

            @Override
            public void onSuccess(String content, BaseBean<ProductDetailBeanWrapper> obj, ProductDetailBeanWrapper detailBean) {

                if (psTab == null) {
                    return;
                }
                if (obj != null || detailBean == null || detailBean.rows != null) {
                    if (obj.isSuccess()) {
                        if (detailBean != null && detailBean.rows != null) {
                            mDetailBean = detailBean;
                            setShareAndCollection(detailBean);
                            updateLicenseStatus(detailBean.licenseStatus, getCurrentLicenesStatusListener());
                            detailBean.rows.licenseStatus = detailBean.licenseStatus;
                            mDetailBean.rows.jgPrice = mDetailBean.getJgProductPrice();
                            producgtId = detailBean.rows.id + "";
                            if (commodityFragment != null) {
                                setPriorityTopTips(detailBean.rows);

                                if (detailBean.actSk != null) {
                                    detailBean.actSk.responseLocalTime = System.currentTimeMillis();
                                }
                                if (detailBean.rows.limitFullDiscountActInfo != null){
                                    detailBean.rows.limitFullDiscountActInfo.setResponseLocalTime(System.currentTimeMillis());
                                }

                                mIsWholeSale = detailBean.getIsWholeSale();
                                commodityFragment.upDataUI(detailBean, detailBean.rows,
                                        detailBean.shopInfo,
                                        detailBean.getIsAssemble(), detailBean.getIsWholeSale(), detailBean.getAct(), detailBean.actSk, detailBean.productPrckagelList);

                            }

                            if (mSpecificationFragment != null) {
                                mSpecificationFragment.upDataUI(detailBean.rows);
                            }
                            // 设置随心拼分享和客服
                            setTitleSuixinpin(detailBean);
                        }
                    } else {//扫码无结果，跳转至无结果提示页，在无结果提示页提示：没有找到相关商品信息 5秒（此处秒针倒计时）后将自动跳转；
                        handleEmptyPage();
                    }
                }
            }

            @Override
            public void onFailure(NetError error) {
                dismissProgress();
                handleEmptyPage();
            }
        });
    }

    /**
     * 在线客服
     */
    private void sendOnLineService(ProductDetailBean detailBean) {
        int isThirdCompany = detailBean != null ? detailBean.isThirdCompany : 0;
        RequestParams params = new RequestParams();
        params.put("isThirdCompany", isThirdCompany + "");

        HttpManager.getInstance().post(AppNetConfig.GET_IM_PACKURL, params, new BaseResponse<ImPackUrlBean>() {

            @Override
            public void onSuccess(String content, BaseBean<ImPackUrlBean> obj, ImPackUrlBean baseBean) {

                if (obj != null && obj.isSuccess()) {

                    if (baseBean != null) {
                        if (isThirdCompany == 1) {
                            RoutersUtils.open(RoutersUtils.getRouterPopCustomerServiceUrl(baseBean.IM_PACK_URL, detailBean.orgId, "", detailBean.companyName));
                        } else {
                            RoutersUtils.open(RoutersUtils.getRouterYbmDetailCustomerServiceUrl(baseBean.IM_PACK_URL));
                        }
                    }
                }

            }

        });

    }

    /**
     * 设置随心拼分享和客服
     */
    private void setTitleSuixinpin(ProductDetailBeanWrapper detailBean) {
        if (detailBean == null || detailBean.actPt == null || detailBean.rows == null) return;

        if(detailBean.actPt.assembleStatus < 2) {
            // 拼团
            if (detailBean.rows.canAddToCart) {
//                llSuixinpin.setVisibility(View.VISIBLE);
//                detailLlCollect.setVisibility(View.GONE);
//                ivNormalShare.setVisibility(View.GONE);
            } else {
                if (detailBean.actPt.supportSuiXinPin) {
//                    llSuixinpin.setVisibility(View.VISIBLE);
//                    detailLlCollect.setVisibility(View.GONE);
//                    ivNormalShare.setVisibility(View.GONE);
                }
            }
        } else if (detailBean.isWholesale == 1) {
            // 批购包邮
            if (detailBean.rows.canAddToCart) {
//                llSuixinpin.setVisibility(View.VISIBLE);
//                detailLlCollect.setVisibility(View.GONE);
//                ivNormalShare.setVisibility(View.GONE);
            } else {
                if (detailBean.actPt.supportSuiXinPin) {
//                    llSuixinpin.setVisibility(View.VISIBLE);
//                    detailLlCollect.setVisibility(View.GONE);
//                    ivNormalShare.setVisibility(View.GONE);
                }
            }
        }

    }

    private void handleEmptyPage() {
        dismissProgress();
        psTab.setVisibility(View.GONE);
        vsNoResult.setVisibility(View.VISIBLE);
        TextView tv_noResult_hint = ButterKnife.findById(ProductDetailActivity.this, R.id.tv_noResult_hint);
        ImageView iv_noResult_back = ButterKnife.findById(ProductDetailActivity.this, R.id.iv_noResult_back);
        if (iv_noResult_back != null) {
            iv_noResult_back.setOnClickListener(v -> finish());
        }
        countUtil = new TimeCountUtil(null, 1000 * 5, 1000);
        countUtil.setCountdownListener(new TimeCountUtil.CountdownListener() {
            @Override
            public void onCountdownIngListener(@Nullable TextView view, @NotNull String secondsNum) {
                if (tv_noResult_hint != null)
                    tv_noResult_hint.setText(Html.fromHtml(String.format(getResources().getString(R.string.str_product_detail_no_result_hint), secondsNum)));
            }

            @Override
            public void onCountdownFinishListener(@Nullable TextView view) {
                finish();
            }
        }).start();
    }


    private int merchantStatus;

    private void setPriorityTopTips(ProductDetailBean rows) {
        merchantStatus = rows.merchantStatus;
        if (rows.distanceEndTime > 0) {
            startCountDown(rows.distanceEndTime);
        }
    }

    /**
     * 开始优先购倒计时
     *
     * @param distanceEndTime
     */
    private void startCountDown(long distanceEndTime) {

        tvTopTipsTitle.setText("智鹿用户专享时段");
        tvTopTipsTitle1.setText("距离结束 ");

        countDownTimer = new CountDownTimer(distanceEndTime, 1000) {
            @Override
            public void onTick(long millisUntilFinished) {
                if (!ProductDetailActivity.this.isFinishing()) {
                    int h = (int) (millisUntilFinished / 1000 / 60 / 60);
                    int m = (int) (millisUntilFinished / 1000 / 60 % 60);
                    int s = (int) (millisUntilFinished / 1000 % 60);

                    tvTipCountdownH.setText(h < 10 ? "0" + h : "" + h);
                    tvTipCountdownM.setText(m < 10 ? "0" + m : "" + m);
                    tvTipCountdownS.setText(s < 10 ? "0" + s : "" + s);
                }
            }

            @Override
            public void onFinish() {
                stopCountDown();
            }
        };
        countDownTimer.start();
        clTopTips.setVisibility(View.VISIBLE);
    }

    /**
     * 结束优先购倒计时
     *
     * @param
     */
    private void stopCountDown() {
        if (commodityFragment != null) {
            // 结束优先购倒计,重新刷新页面
            refreshProductDetailInfo();
        }
        clTopTips.setVisibility(View.GONE);
    }

//    @OnClick({R.id.title_left_b, R.id.tv_menu_b, R.id.iv_normal_share, R.id.iv_share_suixinpin, R.id.iv_kefu_suixinpin})
    @OnClick({R.id.title_left_b, R.id.tv_menu_b, R.id.iv_share_suixinpin, R.id.iv_kefu_suixinpin})
    public void clickTab(View view) {
        switch (view.getId()) {
            case R.id.title_left_b:
                onBackPressed();
                break;
            case R.id.tv_menu_b:
                showPopWindow();
                break;
//            case R.id.iv_normal_share:
            case R.id.iv_share_suixinpin:
                getShareData(false);
                break;
            case R.id.iv_kefu_suixinpin:
                if (mDetailBean != null) {
                    sendOnLineService(mDetailBean.rows);
                }
                break;
        }
    }

    /**
     * 设置弹出popwindow
     */
    private void showPopWindow() {
        JGTrackTopLevelKt.jgTrackProductDetailBtnClick(ProductDetailActivity.this, "更多", jgReferrer);
        LayoutInflater layoutInflater = (LayoutInflater) getSystemService(Context.LAYOUT_INFLATER_SERVICE);
        //自定义的布局文件
        prView = layoutInflater.inflate(R.layout.fragment_commodity_popwindow, null);
        mPw = new PopupWindow(prView, ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        Drawable dr = this.getResources().getDrawable(R.drawable.product_pop_bg);
        // 最重要的就是要确定PopupWindow的弹出位置，使其不管你点击哪个Item都会在它的下发弹出，很简单设置一句代码即可：
        mPw.setBackgroundDrawable(dr);
        // 然后设置点击PopupWindow之外的地方，prowindow消失：
        mPw.setOutsideTouchable(true);
        mPw.setFocusable(true);
        TextView popHome = (TextView) prView.findViewById(R.id.pop_home);
        TextView popSearch = (TextView) prView.findViewById(R.id.pop_search);
        TextView popMessage = (TextView) prView.findViewById(R.id.pop_message);
        TextView popMore = (TextView) prView.findViewById(R.id.pop_more);
        TextView popShare = (TextView) prView.findViewById(R.id.pop_share);
        TextView popCorrection = (TextView) prView.findViewById(R.id.pop_correction);
        TextView popDepreciateInform = (TextView) prView.findViewById(R.id.pop_depreciate_inform);
        TextView popCollection = (TextView) prView.findViewById(R.id.tv_collection_pop);
        LinearLayout llCollectionPop = prView.findViewById(R.id.ll_collect_pop);
        CheckBox cbCollectionPop = prView.findViewById(R.id.cb_collection_pop);
        View vCorrectionLine = prView.findViewById(R.id.v_correction_line);
        View vDepreciateLine = prView.findViewById(R.id.v_depreciate_line);
        View vCollectionLine = prView.findViewById(R.id.v_collection_line);
        cbCollectionPop.setChecked(isFavorite);
        if (isFavorite) {
            popCollection.setText("已收藏");
        } else {
            popCollection.setText("收藏");
        }

        // 控制纠错和降价通知功能的显示
        boolean shouldShowFunction = true;
        if (SpUtil.isKa()) {
            // KA用户隐藏纠错和降价通知功能
            shouldShowFunction = false;
        } else if (mDetailBean != null && mDetailBean.rows != null) {
            // 检查是否为批购包邮模式
            if (mDetailBean.getIsWholeSale()) {
                shouldShowFunction = false;
            }

            // 检查是否为第三方厂家商品
            if (mDetailBean.rows.isThirdCompany == 1) {
                shouldShowFunction = false;
            }

            // 检查是否为赠品
            if (mDetailBean.rows.isShowGive()) {
                shouldShowFunction = false;
            }

            // 注意：不需要根据商品状态（售罄、下架等）隐藏纠错和降价通知功能
            // 因为即使商品售罄或下架，用户仍然可能需要纠错或设置降价通知
        }

        if (shouldShowFunction) {
            popCorrection.setVisibility(View.VISIBLE);
            popDepreciateInform.setVisibility(View.VISIBLE);
            vCorrectionLine.setVisibility(View.VISIBLE);
            vDepreciateLine.setVisibility(View.VISIBLE);
        } else {
            popCorrection.setVisibility(View.GONE);
            popDepreciateInform.setVisibility(View.GONE);
            vCorrectionLine.setVisibility(View.GONE);
            vDepreciateLine.setVisibility(View.GONE);
        }

        View shareLine = prView.findViewById(R.id.v_share_line);
        LinearLayout ll_countent = prView.findViewById(R.id.ll_countent);
//        if (isKaUser) {//隐藏消息按钮
//            popMessage.setVisibility(View.GONE);
//            LinearLayout.LayoutParams params = (LinearLayout.LayoutParams) ll_countent.getLayoutParams();
//            params.height = ConvertUtils.dp2px(180);
//            ll_countent.setLayoutParams(params);
//        }
        if (mPw.isShowing()) {
            // 隐藏窗口，如果设置了点击窗口外小时即不需要此方式隐藏
            mPw.dismiss();
        } else {
            // 显示窗口
            mPw.showAsDropDown(tvMenuB, 0, ConvertUtils.dp2px(6));//view为Listview点击事件传过来的view.
        }
        if (mDetailBean != null && mDetailBean.rows != null && mDetailBean.rows.isShowGive()){ //赠品：显示 首页，搜索，消息，我的
            popHome.setVisibility(View.VISIBLE);
            popSearch.setVisibility(View.VISIBLE);
            popMessage.setVisibility(View.VISIBLE);
            popMore.setVisibility(View.VISIBLE);
            popShare.setVisibility(View.GONE);
            shareLine.setVisibility(View.GONE);
            llCollectionPop.setVisibility(View.GONE);
        }else { //否则显示 首页，搜索，消息，我的，收藏
            popHome.setVisibility(View.VISIBLE);
            popSearch.setVisibility(View.VISIBLE);
            popMessage.setVisibility(View.VISIBLE);
            popMore.setVisibility(View.VISIBLE);
            popShare.setVisibility(View.GONE);
            shareLine.setVisibility(View.VISIBLE);
            llCollectionPop.setVisibility(View.VISIBLE);

        }

        popHome.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                JGTrackTopLevelKt.jgTrackProductDetailBtnClick(ProductDetailActivity.this, "更多-首页", jgReferrer);
                Intent intent = new Intent(ProductDetailActivity.this, MainActivity.class);
                intent.putExtra("comment", "0");
                startActivity(intent);
                finish();
                mPw.dismiss();
            }
        });
        popSearch.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                JGTrackTopLevelKt.jgTrackProductDetailBtnClick(ProductDetailActivity.this, "更多-搜索", jgReferrer);
                RoutersUtils.open("ybmpage://searchproductop");
                XyyIoUtil.track(XyyIoUtil.ACTION_COMMODITYDETAILS_SEARCH, mDetailBean.rows);
                finish();
                mPw.dismiss();
            }
        });
        popMessage.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                JGTrackTopLevelKt.jgTrackProductDetailBtnClick(ProductDetailActivity.this, "更多-消息", jgReferrer);
                Message.openMessagePage();
                finish();
                mPw.dismiss();
            }
        });
        popMore.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                JGTrackTopLevelKt.jgTrackProductDetailBtnClick(ProductDetailActivity.this, "更多-我的", jgReferrer);
                Intent intent = new Intent(ProductDetailActivity.this, MainActivity.class);
                intent.putExtra("comment", "4");
                startActivity(intent);
                finish();
                mPw.dismiss();
            }
        });

        popShare.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                mPw.dismiss();
//                if (producgtId == null) {
                getShareData(false);
            }
        });

        llCollectionPop.setOnClickListener(v -> {
            if (mDetailBean == null || mDetailBean.rows == null) return;
            setCollection(mDetailBean.rows, cbCollectionPop, popCollection, mPw, true);
        });

        // 纠错功能点击事件
        popCorrection.setOnClickListener(v -> {
            mPw.dismiss();
            if (mDetailBean != null && mDetailBean.rows != null) {
                JGTrackTopLevelKt.jgTrackProductDetailBtnClick(ProductDetailActivity.this, "更多-纠错", jgReferrer);
                Intent correctionIntent = new Intent(ProductDetailActivity.this, MainCorrectionActivity.class);
                correctionIntent.putExtra(IntentCanst.SKU_PRICE, mDetailBean.rows.fob);
                correctionIntent.putExtra(IntentCanst.SKU_ID, mDetailBean.rows.id + "");
                startActivity(correctionIntent);
            }
        });

        // 降价通知功能点击事件
        popDepreciateInform.setOnClickListener(v -> {
            mPw.dismiss();
            if (mDetailBean != null && mDetailBean.rows != null) {
                JGTrackTopLevelKt.jgTrackProductDetailBtnClick(ProductDetailActivity.this, "更多-降价通知", jgReferrer);
                String price = UiUtils.transform(mDetailBean.rows.fob);
                Intent intent = new Intent(ProductDetailActivity.this, DepreciateInformActivity.class);
                Bundle bundle = new Bundle();
                bundle.putString("id", mDetailBean.rows.id + "");
                bundle.putString("price", price);
                intent.putExtras(bundle);
                startActivityForResult(intent, 1001); // 使用常量或定义新的请求码
            }
        });
    }

    /**
     * 分享弹框
     */
    private void showShare() {
        if (mDetailBean == null) {
            ToastUtils.showShort("商品信息获取失败");
            return;
        }

        if (shareDialog == null) {
            shareDialog = new GoodsDetailShareDialog(this);
        }
        GoodsDetailShareDialog.GoodsDetailShareDialogRequestBean bean = new GoodsDetailShareDialog.GoodsDetailShareDialogRequestBean(
                mDetailBean.shopInfo.shopName,
                "",
                mDetailBean.rows.showName,
                "",
                mDetailBean.rows.getProductId(),
                mSpellGroupShareContentBean.shareUrl,
                mSpellGroupShareContentBean.title,
                "刚刚在药帮忙看到一个不错的商品，赶快来看看吧",
                false,
                mDetailBean,
                mDetailBean.actPt != null && mDetailBean.actPt.isStepPrice(),
                mDetailBean.rows.promoType + "",
                mDetailBean.rows.promoId + "",
                mDetailBean.rows.markerUrl,
                // <PRD-8>”未签署控销协议商品“显示价格 https://wiki.int.ybm100.com/pages/viewpage.action?pageId=776143984
                // !isNotOEMCondition(mDetailBean.rows) && !isControlUnShowPrice(mDetailBean.rows)
                // 控销商品要显示商品价格；
                (!isNotOEMCondition(mDetailBean.rows) && !isControlUnShowPrice(mDetailBean.rows) || mDetailBean.rows.controlType == 5),
                mDetailBean.rows.controlType == 5,
                mDetailBean.rows.rangePriceBean,
                jgReferrer
        );
        shareDialog.load(bean, mIsWholeSale);
        shareDialog.show();
    }

    /**
     * @return true: 不满足签署协议条件 并且不满足认证资质
     */
    private Boolean isNotOEMCondition(ProductDetailBean productDetail) {
        return (!productDetail.isOEM && productDetail.isControlAgreement == 1 && productDetail.showAgree == 0) ||
                (productDetail.isOEM && productDetail.signStatus == 0 && productDetail.isControlAgreement == 1 && productDetail.showAgree == 0) ||
                (productDetail.isOEM && productDetail.signStatus == 0 && productDetail.isControlAgreement == 0);
    }

    /**
     * @param productDetail
     * @return 是否控销不展示价格
     */
    private Boolean isControlUnShowPrice(ProductDetailBean productDetail) {
        return productDetail.isControlUnShowPrice();
    }

    private SpellGroupShareContentBean mSpellGroupShareContentBean;

    /**
     * 拼团分享弹框
     */
    private void showSpellGroupShareContent() {
        if (mIsWholeSale) {
            HashMap<String, String> trackParams = new HashMap<>();
            trackParams.put("skuId", productId);
            trackParams.put("merchantId", SpUtil.getMerchantid());
            XyyIoUtil.track("share_Freeproduct_details", trackParams);
        }
        if (shareDialog == null) {
            shareDialog = new GoodsDetailShareDialog(this);
        }
        GoodsDetailShareDialog.GoodsDetailShareDialogRequestBean bean = new GoodsDetailShareDialog.GoodsDetailShareDialogRequestBean(
                mDetailBean.shopInfo.shopName,
                AppNetConfig.LORD_IMAGE + mSpellGroupShareContentBean.imageUrl,
                mDetailBean.rows.showName,
                "",
                mDetailBean.rows.getProductId(),
                mSpellGroupShareContentBean.shareUrl,
                mSpellGroupShareContentBean.title,
                mIsWholeSale ? "单品包邮，快来抢购吧" : "成团即包邮，快来参团吧",
                true,
                mDetailBean,
                mDetailBean.actPt != null && mDetailBean.actPt.isStepPrice(),
                mDetailBean.rows.promoType + "",
                mDetailBean.rows.promoId + "",
                mDetailBean.rows.markerUrl,
                // <PRD-8>”未签署控销协议商品“显示价格 https://wiki.int.ybm100.com/pages/viewpage.action?pageId=776143984
                // !isNotOEMCondition(mDetailBean.rows) && !isControlUnShowPrice(mDetailBean.rows)
                // 控销商品要显示商品价格；
                (!isNotOEMCondition(mDetailBean.rows) && !isControlUnShowPrice(mDetailBean.rows) || mDetailBean.rows.controlType == 5),
                mDetailBean.rows.controlType == 5,
                null,
                jgReferrer
        );
        shareDialog.load(bean, mIsWholeSale);
        shareDialog.show();
    }

    /**
     * 获取拼团分享数据
     */
    private void getShareData(boolean isSpellGroup) {
        if (mSpellGroupShareContentBean != null) {
            if (mIsWholeSale || isSpellGroup) {
                showSpellGroupShareContent();
            } else {
                showShare();
            }
            return;
        }
        showProgress();
        RequestParams params = new RequestParams();
        if (mDetailBean == null || mDetailBean.rows == null) {
            return;
        }
        params.put("skuId", mDetailBean.rows.id + "");
        if (mDetailBean.actPt != null && !TextUtils.isEmpty(mDetailBean.actPt.marketingId)) {
            params.put("actId", mDetailBean.actPt.marketingId);
        } else {
            params.put("actId", "0");
        }
        HttpManager.getInstance().post(AppNetConfig.GOODS_DETAIL_SPELL_GROUP_CONTENT, params, new BaseResponse<SpellGroupShareContentBean>() {
            @Override
            public void onSuccess(String content, BaseBean<SpellGroupShareContentBean> obj, SpellGroupShareContentBean spellGroupShareContentBean) {
                super.onSuccess(content, obj, spellGroupShareContentBean);
                if (isDestroy || isFinishing()) return;
                dismissProgress();
                if (obj != null && obj.isSuccess() && spellGroupShareContentBean != null) {
                    mSpellGroupShareContentBean = spellGroupShareContentBean;
                    if (mIsWholeSale || isSpellGroup) {
                        showSpellGroupShareContent();
                    } else {
                        showShare();
                    }
                }
            }

            @Override
            public void onFailure(NetError error) {
                super.onFailure(error);
                if (isDestroy || isFinishing()) return;
                dismissProgress();
            }
        });
    }

    private final int animDuration = 250;//动画执行时间

    private void showAnim(final View view, float start, final float end, int duration, final boolean isWhile) {

        ValueAnimator va = ValueAnimator.ofFloat(start, end).setDuration(duration);
        va.addUpdateListener(new ValueAnimator.AnimatorUpdateListener() {
            @Override
            public void onAnimationUpdate(ValueAnimator animation) {
                float value = (float) animation.getAnimatedValue();
                view.setPivotX(view.getWidth());
                view.setPivotY(0);
                view.setScaleX(value);
                view.setScaleY(value);
            }
        });
        va.addListener(new AnimatorListenerAdapter() {
            @Override
            public void onAnimationEnd(Animator animation) {
                showAnim(view, end, 0.95f, animDuration / 3, false);
            }
        });
        va.start();
    }

    private void alphaAnim(final View view, int start, int end, int duration) {

        ValueAnimator va = ValueAnimator.ofFloat(start, end).setDuration(duration);
        va.addUpdateListener(new ValueAnimator.AnimatorUpdateListener() {
            @Override
            public void onAnimationUpdate(ValueAnimator animation) {
                float value = (float) animation.getAnimatedValue();
                view.setAlpha(value);
            }
        });
        va.start();
    }

    /**
     * 获取拼团推荐（大家都在拼）
     *
     * @param skuid
     */
    private void getGroupProductRecommend(String skuid) {
        if (TextUtils.isEmpty(skuid)) return;
        CommodityViewModel mCommodityViewModel = new ViewModelProvider(this).get(CommodityViewModel.class);
        mCommodityViewModel.getSpellGroupRecommondBean().observe(this, t -> commodityFragment.handleGroupProductRecommend(skuid, t));
        mCommodityViewModel.getGroupRecommendProducts(skuid);
    }

    @Override
    public int getContentViewId() {
        return R.layout.activity_detail3;
    }


    private void initFragment() {
        list_fragment = new ArrayList<>();
        list_fragment.add(commodityFragment);
//        list_fragment.add(mSpecificationFragment);
    }

    @Override
    public boolean onLicenseStatusEnable() {
        return true;
    }

    @Override
    public void handleLicenseStatusChange(int status) {
        refreshProductDetailInfo();
    }

    private void refreshProductDetailInfo() {
        if (!TextUtils.isEmpty(productNo)) {
            doGetNewsContent(null, productNo);
            return;
        }
        if (!TextUtils.isEmpty(productId)) {
            doGetNewsContent(productId, null);
            return;
        }
    }

    @Override
    public void onProductDetailScroll(int position) {
        if (position > 1) return;
        psTab.setCurrentTab(position);
    }

    @Override
    public void onShareCallback() {
        getShareData(true);
    }

    @Override
    public Map<String, Object> registerPageProperties() {

        Map<String, Object> properties = new HashMap<>();
        properties.put(JGTrackManager.FIELD.FIELD_PAGE_ID, JGTrackManager.TrackProductDetail.PAGE_ID);
        properties.put(JGTrackManager.FIELD.FIELD_TITLE, JGTrackManager.TrackProductDetail.TITLE);
        return properties;
    }

    @Override
    public String registerPageUrl() {
        return "com.ybmmarket20.activity.ProductDetailActivity";
    }

    private class MyDetailsAdapter extends FragmentPagerAdapter {

        private List<Fragment> list_fragment;                         //fragment列表
        private List<String> list_Title;                              //tab名的列表

        public MyDetailsAdapter(FragmentManager fm, List<Fragment> list_fragment, List<String> list_Title) {
            super(fm);
            this.list_fragment = list_fragment;
            this.list_Title = list_Title;
        }

        @Override
        public Fragment getItem(int position) {
            return list_fragment.get(position);
        }

        @Override
        public int getCount() {
            return list_fragment.size();
        }

        @Override
        public CharSequence getPageTitle(int position) {
            return list_Title.get(position);
        }

    }

    @Override
    protected void onResume() {
        super.onResume();
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (countUtil != null)
            countUtil.cancel();

        if (countDownTimer != null) {
            countDownTimer.cancel();
            countDownTimer = null;
        }
    }

    @Override
    public void onBackPressed() {
        if (commodityFragment.onBackPress()) return;
        super.onBackPressed();
    }
}
